import time
from typing import Dict, List, Optional, Any
import json
import os
from datetime import datetime

class ResearchProgress:
    """研究进度跟踪器"""
    
    def __init__(self, output_dir: str, project_name: str):
        self.output_dir = output_dir
        self.project_name = project_name
        self.progress_file = os.path.join(output_dir, f"{project_name}_progress.json")
        self.start_time = time.time()
        self.stages = [
            "literature_review",
            "idea_generation",
            "planning",
            "implementation",
            "experimentation",
            "analysis",
            "paper_writing"
        ]
        self.current_stage = None
        self.stage_progress = {stage: 0.0 for stage in self.stages}
        self.stage_start_times = {}
        self.stage_end_times = {}
        self.milestones = []
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 初始化进度文件
        self._save_progress()
    
    def start_stage(self, stage: str) -> None:
        """开始一个新阶段"""
        if stage not in self.stages:
            raise ValueError(f"未知阶段: {stage}")
        
        self.current_stage = stage
        self.stage_start_times[stage] = time.time()
        self._save_progress()
    
    def update_stage_progress(self, progress: float) -> None:
        """更新当前阶段的进度 (0.0 - 1.0)"""
        if self.current_stage is None:
            raise ValueError("没有活跃的阶段")
        
        self.stage_progress[self.current_stage] = min(max(progress, 0.0), 1.0)
        self._save_progress()
    
    def complete_stage(self) -> None:
        """完成当前阶段"""
        if self.current_stage is None:
            raise ValueError("没有活跃的阶段")
        
        self.stage_progress[self.current_stage] = 1.0
        self.stage_end_times[self.current_stage] = time.time()
        self._save_progress()
    
    def add_milestone(self, title: str, description: str) -> None:
        """添加一个里程碑"""
        milestone = {
            "title": title,
            "description": description,
            "timestamp": time.time(),
            "datetime": datetime.now().isoformat()
        }
        self.milestones.append(milestone)
        self._save_progress()
    
    def get_overall_progress(self) -> float:
        """获取总体进度"""
        completed_stages = sum(self.stage_progress.values())
        return completed_stages / len(self.stages)
    
    def get_elapsed_time(self) -> float:
        """获取已用时间（秒）"""
        return time.time() - self.start_time
    
    def _save_progress(self) -> None:
        """保存进度到文件"""
        progress_data = {
            "project_name": self.project_name,
            "start_time": self.start_time,
            "current_time": time.time(),
            "elapsed_time": self.get_elapsed_time(),
            "current_stage": self.current_stage,
            "stage_progress": self.stage_progress,
            "overall_progress": self.get_overall_progress(),
            "stage_start_times": self.stage_start_times,
            "stage_end_times": self.stage_end_times,
            "milestones": self.milestones
        }
        
        with open(self.progress_file, 'w') as f:
            json.dump(progress_data, f, indent=2)