import logging
import traceback
from functools import wraps
from typing import Callable, Any, TypeVar, cast

T = TypeVar('T', bound=Callable[..., Any])

def setup_logger(name: str, log_file: str, level=logging.INFO):
    """设置日志记录器"""
    handler = logging.FileHandler(log_file)
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    
    logger = logging.getLogger(name)
    logger.setLevel(level)
    logger.addHandler(handler)
    
    return logger

def error_handler(func: T) -> T:
    """装饰器：捕获并记录函数执行期间的错误"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logger = logging.getLogger("error_handler")
            logger.error(f"Error in {func.__name__}: {str(e)}")
            logger.error(traceback.format_exc())
            raise
    return cast(T, wrapper)