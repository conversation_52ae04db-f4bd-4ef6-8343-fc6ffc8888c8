import argparse
import os
import sys
from typing import Dict, Any, List
import json

def parse_arguments() -> Dict[str, Any]:
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="AI-Researcher: 自动化科学研究助手")
    
    # 主要命令
    subparsers = parser.add_subparsers(dest="command", help="要执行的命令")
    
    # 研究计划命令
    plan_parser = subparsers.add_parser("plan", help="生成研究计划")
    plan_parser.add_argument("--instance_path", required=True, help="实例文件路径")
    plan_parser.add_argument("--container_name", required=True, help="Docker容器名称")
    plan_parser.add_argument("--model", default="gpt-4o-2024-08-06", help="使用的模型")
    plan_parser.add_argument("--workplace_name", default="workplace", help="工作区名称")
    plan_parser.add_argument("--max_iter_times", type=int, default=3, help="最大迭代次数")
    plan_parser.add_argument("--category", help="类别")
    
    # 研究想法命令
    idea_parser = subparsers.add_parser("idea", help="生成研究想法")
    idea_parser.add_argument("--instance_path", required=True, help="实例文件路径")
    idea_parser.add_argument("--container_name", required=True, help="Docker容器名称")
    idea_parser.add_argument("--model", default="gpt-4o-2024-08-06", help="使用的模型")
    idea_parser.add_argument("--workplace_name", default="workplace", help="工作区名称")
    idea_parser.add_argument("--max_iter_times", type=int, default=3, help="最大迭代次数")
    idea_parser.add_argument("--category", help="类别")
    
    # 论文生成命令
    paper_parser = subparsers.add_parser("paper", help="生成研究论文")
    paper_parser.add_argument("--research_dir", required=True, help="研究结果目录")
    paper_parser.add_argument("--output_dir", required=True, help="输出目录")
    paper_parser.add_argument("--model", default="gpt-4o-2024-08-06", help="使用的模型")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        sys.exit(1)
    
    return vars(args)

def execute_command(args: Dict[str, Any]) -> None:
    """执行命令"""
    command = args.pop("command")
    
    if command == "plan":
        from research_agent.run_infer_plan import main as run_plan
        run_plan(**args)
    elif command == "idea":
        from research_agent.run_infer_idea import main as run_idea
        run_idea(**args)
    elif command == "paper":
        # 导入论文生成模块
        pass
    else:
        print(f"未知命令: {command}")
        sys.exit(1)

def main():
    """主函数"""
    args = parse_arguments()
    execute_command(args)

if __name__ == "__main__":
    main()