current_dir=$(dirname "$(readlink -f "$0")")
cd $current_dir
export DOCKER_WORKPLACE_NAME=workplace_paper

export BASE_IMAGES=tjbtech1/paperagent:latest

export COMPLETION_MODEL="openai/gpt-4o-2024-08-06"
export CHEEP_MODEL="openai/gpt-4o-2024-08-06"
export API_BASE_URL="http://localhost:8321/v1"
export EMBEDDING_MODEL="/media/sc/AI/self-llm/embed_model/sentence-transformers/all-MiniLM-L6-v2"
export GPUS='"device=0"'

category=vq
instance_id=one_layer_vq
cd research_agent
python run_infer_plan.py \
    --instance_path ../benchmark/final/${category}/${instance_id}.json \
    --model "openai/gpt-4o-2024-08-06" \
    --container_name "test_one_container" \
    --workplace_name "workplace_paper" \
    --max_iter_times 0 \
    --category ${category}