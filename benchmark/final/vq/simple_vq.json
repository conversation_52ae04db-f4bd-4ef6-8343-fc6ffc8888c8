{"target": "Addressing Representation Collapse in Vector Quantized Models with One Linear Layer", "instance_id": "simple_vq", "authors": ["<PERSON><PERSON>", "Bocheng Li", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "year": 2024, "url": "http://arxiv.org/abs/2411.02038v1", "abstract": "Vector Quantization (VQ) is a widely used method for converting continuous representations into discrete codes. This study proposes SimVQ, a novel method which reparameterizes the code vectors through a linear transformation layer based on a learnable latent basis.", "venue": "arXiv.org", "venue_source": "Semantic Scholar", "venue_lookup_time": "2025-01-10T19:25:07.654545", "citations": 2, "topic": "selected", "field": "selected", "source_papers": [{"reference": "Neural discrete representation learning", "rank": 1, "type": ["methodological foundation"], "justification": "This paper introduces Vector Quantization (VQ) as a foundational technique for encoding data into discrete representations.", "usage": "The core VQ method proposed in this study is directly utilized in the proposed model, providing the essential framework for vector quantization."}, {"reference": "Vector-quantized image modeling with improved VQGAN", "rank": 2, "type": ["methodological foundation"], "justification": "This work enhances the original VQ-VAE framework by integrating adversarial networks.", "usage": "The improved VQGAN methodology is built upon to develop the proposed model."}, {"reference": "Taming transformers for high-resolution image synthesis", "rank": 3, "type": ["methodological foundation"], "justification": "This study presents VQGAN which combines Vector Quantization with adversarial training.", "usage": "VQGAN serves as a foundational model that the proposed model builds upon."}], "task1": "Select 5 key VQ-VAE and VQGAN reference codebases from GitHub for implementing a linear transformation layer to address representation collapse in vector quantized models. Focus on PyTorch implementations with clear model architectures. Limit analysis to essential repositories to avoid tool call overload.", "task2": "The primary task is to address representation collapse in vector quantized models by implementing a linear transformation layer that optimizes the entire codebook rather than individual code vectors."}